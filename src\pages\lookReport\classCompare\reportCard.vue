<template>
  <div class="reportCard" v-loading="tableLoading">
    <div class="reportCard-title clearfix">
      <div class="reportCard-title-box pull-left">
        <div class="titleLine">成绩单</div>
        <el-popover
          v-if="typeList.length"
          placement="bottom"
          width="400"
          trigger="click"
          popper-class="popover"
          v-model="isPopoverVisible"
        >
          <el-checkbox-group class="checkbox-type-group" v-model="tempCheckTypeList">
            <el-checkbox
              v-for="item in typeList"
              :key="item.value"
              class="checkbox"
              :label="item.value"
              :disabled="item.disabled"
              >{{ item.label }}</el-checkbox
            >
          </el-checkbox-group>
          <div class="popover-footer">
            <el-button size="small" @click="isPopoverVisible = false">取消</el-button>
            <el-button type="primary" size="small" @click="handleCheckType">确定</el-button>
          </div>
          <el-button class="filtrate" slot="reference" type="text"
            >指标筛选 <i class="el-icon-arrow-down"></i
          ></el-button>
        </el-popover>
        <!-- <div>指标筛选</div> -->
        <!-- 添加对比考试按钮 -->
        <contrast-report-btn
          v-if="contrastEnable"
          class="contrast-btn"
          label="查看学生升降"
          :examName="contrastObj.examName"
          :examId="contrastObj.examId"
          @updateData="getReportCard"
        >
        </contrast-report-btn>
      </div>

      <div class="reportCard-title-box pull-right">
        <el-button type="primary" class="not-exam-stu" @click="isShowMissDialog = true">缺考名单</el-button>
        <div class="header__serarch clearfix display_flex">
          <el-input
            class="search__text"
            placeholder="输入学号或姓名搜索"
            v-model.trim="searchValue"
            @keyup.enter.native="searchReport"
            clearable
          >
          </el-input>
          <div
            class="search__icon el-icon-search display_flex align-items_center justify-content_flex-center"
            @click="searchReport"
          ></div>
        </div>
        <el-button type="primary" class="export-btn" @click="isShowReportExport = true">导出</el-button>
      </div>
    </div>

    <div v-if="tableData.length">
      <el-table
        :data="tableData"
        ref="tableRef"
        style="width: 100%"
        stripe
        border
        class="reportCard-table"
        align="center"
        :header-cell-class-name="'table-sort-cell'"
        :header-cell-style="{ fontSize: '14px', color: '#3F4A54', backgroundColor: '#f5f7fa' }"
        :default-sort="defaultSort"
        v-drag-table
        v-sticky-table="0"
        row-key="stuId"
        @sort-change="sortChange"
      >
        <el-table-column
          sortable="custom"
          prop="stuNo"
          label="学号"
          :min-width="100"
          :fixed="true"
          :showOverflowTooltip="true"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="clsName"
          label="班级"
          :min-width="100"
          :fixed="true"
          :showOverflowTooltip="true"
        >
        </el-table-column>
        <el-table-column
          sortable="custom"
          prop="stuName"
          label="姓名"
          :min-width="100"
          :fixed="true"
          :showOverflowTooltip="true"
        >
        </el-table-column>

        <template v-if="canShowOrient">
          <el-table-column
            v-if="checkTypeList.includes('orientAlias')"
            prop="orient.alias"
            label="选科组合"
            :min-width="100"
            :showOverflowTooltip="true"
          >
            <template #default="scope">
              <span>{{ scope.row.orient?.alias || '--' }}</span>
            </template>
          </el-table-column>
          <el-table-column
            v-if="
              filterData.qType == 0 ? checkTypeList.includes('orientScore') : checkTypeList.includes('orientRuleScore')
            "
            prop="orient.score"
            :label="filterData.qType == 0 ? '选考得分' : '选考赋分'"
            :min-width="100"
            :showOverflowTooltip="true"
          >
            <template #default="scope">
              <span>{{
                scope.row.orient?.alias
                  ? filterData.qType == 0
                    ? scope.row.orient?.score
                    : scope.row.orient?.rule?.score
                  : '--'
              }}</span>
            </template>
          </el-table-column>
        </template>

        <template v-for="(item, index) in subjectList">
          <el-table-column
            v-if="item.cols.some(col => isColumnVisible(col))"
            :key="item.id"
            :label="`${item.name}(${item.fullScore})`"
            :min-width="90"
            :showOverflowTooltip="true"
          >
            <template v-for="prop in item.cols">
              <el-table-column
                v-if="isColumnVisible(prop)"
                :key="prop"
                :label="fieldMap[prop]"
                :prop="prop"
                :min-width="100"
                :width="prop === 'ruleName' ? 180 : ''"
                :sortable="isSortable(item, prop)"
                :showOverflowTooltip="true"
              >
                <template #default="scope">
                  <span v-if="showLock(prop)">
                    <el-tooltip effect="dark" content="应相关部门/学校要求，该数据不予展示" placement="top">
                      <i class="el-icon-lock"></i>
                    </el-tooltip>
                  </span>
                  <span
                    v-else
                    :style="{
                      color:
                        (prop === 'clsRankUp' || prop === 'grdRankUp') && scope.row.subjects[item.name][prop] < 0
                          ? 'red'
                          : 'inherit',
                    }"
                  >
                    {{ getDefaultRowData(scope.row.subjects[item.name][prop]) }}
                  </span>
                </template>
              </el-table-column>
            </template>
          </el-table-column>
        </template>

        <el-table-column v-if="filterData.source == 0" label="操作" fixed="right" :min-width="140">
          <template #default="scope">
            <el-button type="text" size="small" @click="handleClick(scope.row)">查看详情</el-button>
            <el-button v-if="canViewPaper" type="text" size="small" @click="goToPaper(scope.row)">查看原卷</el-button>
            <el-button v-if="isSmart == 2" type="text" size="small" @click="goToSmartMark(scope.row)"
              >查看智批</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        class="pagination"
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        layout="total, prev, pager, next, sizes"
        :current-page.sync="pagination.page"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        :total="pagination.total"
      >
      </el-pagination>
    </div>

    <no-data v-else text="暂无学生成绩数据" style="height: 500px"></no-data>

    <StuPaperPreview
      v-if="isShowPaperImg"
      :queryParams="paperQueryParams"
      :defaultScore="currentScore"
      @close="isShowPaperImg = false"
    ></StuPaperPreview>

    <StuSmartPreview
      v-if="isShowSmartMark"
      :studentNo="paperQueryParams.studentNo"
      :personalBookId="personalBookId"
      @close="isShowSmartMark = false"
    ></StuSmartPreview>

    <miss-stu-dialog
      v-if="isShowMissDialog"
      :subjectId="filterData.subjectId"
      :clzId="filterData.classId"
      @closed="isShowMissDialog = false"
    ></miss-stu-dialog>
    <el-backtop target=".reportDetail__main"></el-backtop>

    <report-export-dialog
      v-if="isShowReportExport"
      :isRule="isCurrectRule"
      :filterData="filterData"
      :withExamId="contrastObj.examId"
      :isGrdRankEnable="isGrdRankEnable"
      :isClsRankEnable="isClsRankEnable"
      :onlyLv="onlyLv"
      :indicatorList="typeList"
      :defaultIndicators="checkTypeList"
      @closed="isShowReportExport = false"
    >
    </report-export-dialog>
  </div>
</template>

<script>
import LookPaperDialog from '@/components/LookPaperDialog.vue';
import StuPaperPreview from '@/components/SwiperViewer/StuPaperPreview.vue';
import ContrastReportBtn from '@/components/contrastReportBtn.vue';
import NoData from '@/components/noData.vue';
import {
  getNewReportCard,
  getReportCard,
  getSchCfgAPI,
  getStuQuesAnalyze,
  getStuScanDataAPI,
  listStuGroup,
} from '@/service/pexam';
import { sessionSave, getToRoles, findIntersection, isCef } from '@/utils/index.js';
import MissStuDialog from './reportCard/missStuDialog.vue';

import BaseTable from '@/components/Base/table/BaseTable';
import BaseTableColumn from '@/components/Base/table/BaseTableColumn';

import UserRole from '@/utils/UserRole';
import ReportExportDialog from './reportCard/ReportExportDialog.vue';
import { getViewPaper } from '@/service/testbank';
import { FuncIdEnum, getFuncEnable, indicatorManager } from '@/utils/examReportUtils';
import StuSmartPreview from '@/components/SwiperViewer/StuSmartPreview.vue';
import { ISOURCE_TYPES } from '@/typings/card';
import { SchoolSettingType } from '@/pages/schoolSetting/types';

// 获取表格默认展示数据
function getDefaultRowData(data) {
  return data || data === 0 ? data : '--';
}

const fieldMap = {
  score: '得分',
  grdRank: '校排',
  grdRankUp: '升降',
  clsRank: '班排',
  clsRankUp: '升降',
  ruleScore: '赋分',
  ruleName: '等级',
  ruleGrdRank: '校排',
  ruleClsRank: '班排',
  clsLv: '等级',
  grdLv: '等级',
};

export default {
  name: 'report-card',
  props: ['filterData'],
  data() {
    return {
      fieldMap,
      // 搜索值
      searchValue: '',
      // 分页
      pagination: {
        page: 1,
        pageSize: 20,
        total: 0,
        pageCount: 1,
      },
      // 表格loading
      tableLoading: false,
      // 表格数据
      tableData: [],
      // 当前表格排序规则
      sortType: '',
      // 对比考试
      contrastObj: {
        examName: '',
        examId: '',
      },
      // 当前分数
      currentScore: 0,
      // 学科列表
      subjectList: [],
      // 规则模式
      ruleMode: '',
      // 是否显示导出对话框
      isShowReportExport: false,
      // 是否启用校排功能权限
      isGrdRankEnable: true,
      // 是否启用班排功能权限
      isClsRankEnable: true,
      //是否展示原卷
      isShowPaperImg: false,
      // 是否显示对比考试弹窗
      isShowContrastReportDialog: false,
      // 是否显示缺考对话框
      isShowMissDialog: false,
      // 默认排序
      defaultSort: {
        prop: 'grdRank',
        order: 'ascending',
      },

      // 试卷查询参数
      paperQueryParams: {
        workId: '',
        examId: '',
        classId: '',
        subjectId: '',
        studentNo: '',
        studentId: '',
        cardType: '',
      },
      // 是否显示智批改
      isShowSmartMark: false,
      // 只显示等级
      onlyLv: this.$sessionSave.get('onlyLv'),
      // 指标列表
      typeList: indicatorManager.scoreCardIndicatorList,
      // 筛选类型
      checkTypeList: [],
      // 暂存筛选类型
      tempCheckTypeList: [],
      // 是否显示弹出框
      isPopoverVisible: false,
    };
  },

  computed: {
    // 学科来源
    subjectItem() {
      let subjectId = this.filterData.subjectId;
      let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
      let subjectItem = subjectList.find(item => item.id == subjectId);
      return subjectItem;
    },

    // 是否可以查看原卷
    canViewPaper() {
      return (
        this.filterData.subjectId &&
        (this.subjectItem.source == ISOURCE_TYPES.HAND ||
          this.subjectItem.source == ISOURCE_TYPES.WEB ||
          this.subjectItem.source == ISOURCE_TYPES.PHOTO)
      );
    },

    // 智批改类型 0:末开启 1:作文智批改 2.试卷智批改
    isSmart() {
      return this.subjectItem?.isSmart || 0;
    },

    // personalBookId
    personalBookId() {
      return this.subjectItem?.personalBookId || '';
    },

    paperNo() {
      return this.subjectItem?.paperNo || '';
    },

    workId() {
      if (this.subjectItem) {
        return this.subjectItem.workIds[Number(this.filterData.abPaper)];
      }
      return '';
    },

    // 主workId
    mainWorkId() {
      return this.subjectItem?.workId || '';
    },

    // 是否主报告
    isMainReport() {
      return this.$sessionSave.get('reportDetail').examId == this.$sessionSave.get('reportParent').examId;
    },

    // 是否赋分
    isCurrectRule() {
      let subjectList = this.$sessionSave.get('innerNoRoleSubjectList');
      if (this.subjectItem) {
        return this.subjectItem.isRule;
      }
      return subjectList.some(t => t.isRule);
    },

    // 是否可以显示等级
    canShowLv() {
      return this.filterData.qType == 0 && this.filterData.source !== 1 && !this.onlyLv;
    },

    // 是否可以显示选科
    canShowOrient() {
      return (this.ruleMode == '3+1+2' || this.filterData.source == 1) && !this.onlyLv;
    },

    // 是否可以对比
    contrastEnable() {
      return this.isMainReport && this.filterData.abPaper !== '0' && this.filterData.abPaper !== '1' && !this.onlyLv;
    },
  },
  components: {
    LookPaperDialog,
    MissStuDialog,
    StuPaperPreview,
    NoData,
    BaseTable,
    BaseTableColumn,
    ContrastReportBtn,
    ReportExportDialog,
    StuSmartPreview,
  },
  watch: {
    filterData: {
      deep: true,
      handler(newValue, oldValue) {
        this.initData();
      },
    },
    isPopoverVisible: {
      handler() {
        this.tempCheckTypeList = JSON.parse(JSON.stringify(this.checkTypeList));
      },
    },
  },

  mounted() {
    this.searchValue = this.$sessionSave.get('reportCardSearchVal') || '';
    let queryData = this.$route.query;
    let contrastObj = this.$sessionSave.get('contrastObj');
    if (contrastObj) {
      this.contrastObj.examId = contrastObj.examId;
      this.contrastObj.examName = contrastObj.examName;
    }
    if (queryData.from === 'cardDetail') {
      this.$emit('showTotalClass');
    }
    this.checkTypeList = indicatorManager.getIndicator('cardHome');
    this.initData();
  },
  beforeRouteLeave(to, from, next) {
    if (to.path.indexOf('reportCard') === -1) {
      this.$sessionSave.set('reportCardSearchVal', '');
    }
    next();
  },
  beforeDestroy() {
    this.$sessionSave.set('backContrastObj', false);
  },
  methods: {
    getDefaultRowData,

    async initData(data, type) {
      this.isGrdRankEnable = getFuncEnable({ funcId: FuncIdEnum.GrdRankAscDesc, classId: this.filterData.classId });
      this.isClsRankEnable = getFuncEnable({ funcId: FuncIdEnum.ClsRankAscDesc, classId: this.filterData.classId });
      if (this.filterData.qType == 0) {
        let sortType = this.isGrdRankEnable ? 'grdRank' : 'clsRank';
        this.sortType = sortType;
        this.defaultSort = {
          prop: sortType,
          order: 'ascending',
        };
      } else {
        let sortType = this.isGrdRankEnable ? 'ruleGrdRank' : 'ruleClsRank';
        this.sortType = sortType;
        this.defaultSort = {
          prop: sortType,
          order: 'ascending',
        };
      }
      this.tableData = [];
      await this.$nextTick();
      this.pagination.page = 1;
      this.getReportCard('', type);
    },

    // 班级学科初始化后更新数据
    async updateFilter(data, type) {},

    // 分页查询
    handleCurrentChange(val) {
      this.pagination.page = val;
      this.getReportCard();
    },

    handleSizeChange(val) {
      this.pagination.page = 1;
      this.pagination.pageSize = val;
      this.getReportCard();
    },

    // 搜索学生
    async searchReport() {
      this.pagination.page = 1;
      if (this.$refs.tableRef) this.$refs.tableRef.bodyWrapper.scrollTop = 0;
      this.tableData = [];
      await this.$nextTick();
      this.getReportCard();
    },
    // 更改排名排序类型
    async changeQType() {
      if (this.$refs.tableRef) this.$refs.tableRef.bodyWrapper.scrollTop = 0;
      this.initData();
    },

    loadStuMore() {
      if (this.pagination.page >= Math.ceil(this.pagination.total / this.pagination.pageSize)) return;
      this.pagination.page++;
      this.getReportCard();
    },

    // 获取成绩单
    async getReportCard(reportObj) {
      return this.getStuReport(reportObj);
    },

    // 搜索数据
    async getStuReport(reportObj, type) {
      // 添加了对比考试
      if (reportObj) {
        this.contrastObj = reportObj;
        this.$sessionSave.set('contrastObj', this.contrastObj); // 保存对比考试，切换目录时还原
      }
      this.isGrdRankEnable = getFuncEnable({ funcId: FuncIdEnum.GrdRankAscDesc, classId: this.filterData.classId });
      this.isClsRankEnable = getFuncEnable({ funcId: FuncIdEnum.ClsRankAscDesc, classId: this.filterData.classId });

      this.tableLoading = true;
      try {
        const { data } = await getNewReportCard({
          examId: this.$sessionSave.get('reportDetail').examId,
          classId: this.filterData.classId,
          subjectId: this.filterData.source == 0 ? this.filterData.subjectId : this.filterData.aliasName,
          withExamId: this.contrastObj.examId || '',
          text: this.searchValue,
          sort: this.sortType,
          qType: this.filterData.qType,
          page: this.pagination.page,
          pageSize: this.pagination.pageSize,
          v: this.$sessionSave.get('reportDetail').v,
          source: this.filterData.source,
          abPaper: this.filterData.abPaper,
        });

        if (!reportObj && data.rows.contrast?.name) {
          this.contrastObj.examName = data.rows.contrast.name;
          this.contrastObj.examId = data.rows.contrast.id;
        }

        this.ruleMode = data.rows.ruleMode;
        this.tableData = data.rows.stuList;
        this.subjectList = data.rows.subjectList;

        if (this.filterData.source == 0) {
          let innerSubjectList = this.$sessionSave.get('innerSubjectList');
          let subjectList = data.rows.subjectList;
          let newSubjectList = [];
          newSubjectList = subjectList.filter(item => {
            const ids = item.id.split('-');
            return item.id == 'zf' || item.id == 'ysw' || innerSubjectList.some(t => ids.includes(String(t.id)));
          });
          if (this.onlyLv) {
            newSubjectList = newSubjectList.filter(item => {
              return item.cols.some(col => ['clsLv', 'grdLv', 'ruleName'].includes(col));
            });
          }
          let innerClassList = this.$sessionSave.get('innerClassList');
          this.subjectList = newSubjectList;
        }
        this.getTypeList();
        this.pagination.total = data.total_rows;
      } catch (error) {
        console.error(error);
      }
      this.tableLoading = false;
    },

    getTypeList() {
      const cols = this.subjectList
        .map(item => item.cols)
        .flat()
        .filter((item, index, self) => self.indexOf(item) === index); // 去重
      if (!this.isCurrectRule) {
        this.typeList = indicatorManager.scoreCardIndicatorList.filter(item => {
          return ['score', 'grdRank', 'clsRank', 'lv'].some(t => t == item.value);
        });
      }
      if (!this.isGrdRankEnable) {
        // 将grdRank的disabled设置为true
        this.typeList = this.typeList.map(item => {
          if (item.value == 'grdRank') {
            item.disabled = true;
          }
          return item;
        });
      }
      if (!this.isClsRankEnable) {
        this.typeList = this.typeList.map(item => {
          if (item.value == 'clsRank') {
            item.disabled = true;
          }
          return item;
        });
      }

      // 只显示等级
      if (this.onlyLv) {
        this.typeList = [];
      }
    },

    //查看原卷
    async goToPaper(item) {
      const subjectItem = this.$sessionSave.get('innerSubjectList').find(item => item.id == this.filterData.subjectId);

      if (isCef()) {
        this.paperQueryParams = {
          workId: this.mainWorkId,
          examId: this.$sessionSave.get('reportDetail').examId,
          classId: item.classId,
          subjectId: this.filterData.subjectId,
          studentNo: item.stuNo,
          studentId: item.stuId,
          abPaper: item.abPaper,
        };
        this.currentScore =
          item.subjects[this.filterData.subjectName]['score'] ||
          item.subjects[this.filterData.subjectName]['ruleScore'] ||
          0;
        this.isShowPaperImg = true;
      } else {
        this.$router.push({
          path: '/home/<USER>',
          query: {
            workId: this.mainWorkId,
            examId: this.$sessionSave.get('reportDetail').examId,
            classId: item.classId,
            subjectId: this.filterData.subjectId,
            studentNo: item.stuNo,
            studentId: item.stuId,
            abPaper: item.abPaper,
            source: subjectItem.source,
          },
        });
      }
    },

    // 点击查看详情
    handleClick(item) {
      this.$sessionSave.set('reportCardSearchVal', this.searchValue);
      let base = '/home';
      if (this.$route.path.includes('dReport')) {
        base = '/dReport';
      }

      this.$router.push({
        path: base + '/reportDetail/classCompare/cardDetail',
        query: {
          quesIds: item.quesIds,
          sortType: this.sortType,
          stuId: item.stuId,
          clsId: item.classId,
          withExamId: this.contrastObj.examId || '',
          examName: this.contrastObj.examName,
          abPaper: item.abPaper,
        },
      });
    },

    // 排序
    sortChange({ prop, order }) {
      this.pagination.page = 1;
      if (order === 'ascending') {
        this.sortType = prop;
      } else if (order === 'descending') {
        this.sortType = '-' + prop;
      } else {
        this.sortType = '';
      }
      this.getReportCard();
    },

    // 是否显示锁
    showLock(prop) {
      if (prop == 'grdRank' || prop == 'ruleGrdRank' || prop == 'grdRankUp') {
        return !this.isGrdRankEnable;
      }

      if (prop == 'clsRank' || prop == 'ruleClsRank' || prop == 'clsRankUp') {
        return !this.isClsRankEnable;
      }

      return false;
    },

    /**
     * 判断列是否应该显示
     * @param col 列标识
     */
    isColumnVisible(col) {
      let visible = true;
      if (this.onlyLv) {
        if (['clsLv', 'grdLv', 'ruleName'].includes(col)) {
          visible = true;
        } else {
          visible = false;
        }
      } else if (['clsLv', 'grdLv'].includes(col)) {
        visible = this.canShowLv ? this.checkTypeList.includes('lv') : true;
      } else {
        visible = this.checkTypeList.includes(col);
        if (['grdRankUp', 'clsRankUp'].includes(col)) {
          visible =
            !(this.filterData.abPaper === '1' || this.filterData.abPaper === '0') &&
            ((col == 'grdRankUp' && this.checkTypeList.includes('grdRank')) ||
              (col == 'clsRankUp' && this.checkTypeList.includes('clsRank')));
        }
      }
      return visible;
    },

    /**
     * 判断列是否可排序
     * @param item 当前列的科目
     * @param col 列标识
     */
    isSortable(item, col) {
      let sortable = false;
      const isMatchSubject =
        (this.filterData.subjectId && this.filterData.subjectId === item.id) ||
        (!this.filterData.subjectId && item.name === '总分');

      // 判断是否为可排序的列(班排、校排等)
      const sortableColumns = ['clsRank', 'grdRank', 'ruleGrdRank', 'ruleClsRank'];
      if (isMatchSubject && sortableColumns.includes(col)) {
        if (this.showLock(col)) {
          sortable = false;
        } else {
          sortable = 'custom';
        }
      }
      return sortable;
    },

    // 显示智能批注
    goToSmartMark(item) {
      this.paperQueryParams = {
        workId: this.mainWorkId,
        examId: this.$sessionSave.get('reportParent').examId,
        classId: item.classId,
        subjectId: this.filterData.subjectId,
        studentNo: item.stuNo,
        studentId: item.stuId,
        abPaper: item.abPaper,
      };
      this.isShowSmartMark = true;
    },

    handleCheckType() {
      this.checkTypeList = JSON.parse(JSON.stringify(this.tempCheckTypeList));
      this.isPopoverVisible = false;
      indicatorManager.setIndicator('cardHome', this.checkTypeList);
    },
  },
};
</script>

<style lang="scss" scoped>
.titleLine {
  display: inline-block;
  position: relative;
  height: 54px;
  line-height: 54px;
  font-size: 16px;
  font-weight: bold;
  color: #3f4a54;
  padding-left: 16px;
  &:before {
    content: '';
    width: 6px;
    height: 24px;
    background: #409eff;
    border-radius: 3px;
    position: absolute;
    left: 0;
    top: 15px;
  }
}

.reportCard-title {
  margin: 15px 0 18px;

  .selectClass {
    color: #409eff;
    font-size: 16px;
    margin-right: 20px;
    .el-icon-close {
      margin-left: 5px;
      cursor: pointer;
    }
  }
  .addCompare {
    width: 136px;
    height: 36px;
    padding: 0;
    text-align: center;
    line-height: 35px;
    background: #409eff;
    border-radius: 4px;
    color: #fff;
  }
  .contrast-btn {
    flex: 1;
  }

  .level-switch {
    height: 100%;
  }

  .type-select {
    width: 140px;
    margin-right: 25px;
  }

  .reportCard-title-box {
    display: flex;
    align-items: center;

    > * {
      margin-right: 10px;
    }
  }

  .header__serarch {
    display: flex;
    width: 240px;
    .search__icon {
      width: 38px;
      font-size: 18px;
      color: #fff;
      background: #409eff;
      border-radius: 0 3px 3px 0;
      outline: none;
      cursor: pointer;
    }
  }
}

.pagination {
  text-align: center;
  margin-top: 20px;
}
.not-exam-stu {
  margin-right: 20px;
}
.export-btn {
  margin-left: 20px;
}
.popover-footer {
  margin-top: 10px;
  text-align: right;
}
.checkbox-type-group {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  column-gap: 10px;
  row-gap: 10px;
}
</style>
<style lang="scss">
.search__text {
  .el-input__inner {
    border-radius: 4px 0 0 4px;
  }
}

.reportCard-table {
  &.el-table th,
  .el-table td,
  &.el-table .cell {
    text-align: center;
  }
  &.el-table th.is-leaf {
    border-bottom: 0.5px solid #ebeef5;
  }
}
</style>
